'''
                Summary of What to Change:

Parameter	        Old Range	        New (Broader) Range
stop_loss	        -0.5 to -1.5	    -0.3 to -2.0
target_profit	    1.0 to 2.0	        0.5 to 3.0
ratio_check	        0.1 to 0.2	        0.05 to 0.30
resize_factor	    0.3 to 0.7	        0.1 to 1.0
max_evals	        50	                300
exit_time	        14:45 to 15:15	    14:00 to 15:30
start_time	        09:30 to 09:55	    09:15 to 10:55
'''


import os
import random
import csv
import re
import numpy as np
import pandas as pd
from datetime import datetime
from hyperopt import fmin, tpe, hp, Trials, STATUS_OK
from BT_Thursday_MainCode import run_for_one_day
from Matrix import *
from dotenv import load_dotenv
from load_parquet_in_memory import load_parquet_data, list_date_folders


load_dotenv()


# ------------------ Helper to Remove Duplicate Trials ------------------ #
def get_unique_sorted_trials(trials):
    seen = set()
    unique_trials = []
    for trial in sorted(trials.results, key=lambda x: x['loss']):
        param_tuple = tuple(sorted(trial['params'].items()))
        if param_tuple not in seen:
            seen.add(param_tuple)
            unique_trials.append(trial)
    return unique_trials


# ------------------ Save ALL Unique Trials to CSV ------------------ #
def save_results_to_csv(trials, file_path):
    seen = set()
    unique_rows = []
    Capital = int(os.getenv('Capital'))

    for trial in trials.results:
        params = trial['params']
        param_tuple = tuple(sorted(params.items()))
        if param_tuple not in seen:
            seen.add(param_tuple)
            total_pnl = trial.get('total_pnl', 0)
            max_drawdown = trial.get('max_drawdown', 0)
            normalized_pnl = total_pnl / Capital if Capital else 0
            risk_penalty = trial.get('risk_penalty', 1)

            unique_rows.append({
                'start_time': params['start_time'],
                'exit_time': params['exit_time'],
                'stop_loss': params['stop_loss'],
                'target_profit': params['target_profit'],
                'ratio_check': params['ratio_check'],
                'resize_factor': params['resize_factor'],
                'total_pnl': total_pnl,
                'normalized_pnl': normalized_pnl,
                'max_drawdown': max_drawdown,
                'risk_penalty': risk_penalty,
                'final_score': -trial.get('loss', 0)
            })

    # ✅ Sort by final_score descending
    unique_rows.sort(key=lambda x: x['final_score'], reverse=True)

    with open(file_path, mode='w', newline='') as file:
        writer = csv.DictWriter(file, fieldnames=[
            'start_time', 'exit_time', 'stop_loss', 'target_profit',
            'ratio_check', 'resize_factor', 'total_pnl', 'normalized_pnl',
            'max_drawdown',  'risk_penalty','final_score'
        ])
        writer.writeheader()
        writer.writerows(unique_rows)


# ------------------ Objective Function ------------------ #
def objective(params, all_folders):
     # 🔧 Round sensitive continuous parameters
    params['stop_loss'] = round(params['stop_loss'], 2)
    params['target_profit'] = round(params['target_profit'], 2)
    params['ratio_check'] = round(params['ratio_check'], 3)
    params['resize_factor'] = round(params['resize_factor'], 2)

    start_time = datetime.strptime(params['start_time'], "%H:%M").time()
    exit_time = datetime.strptime(params['exit_time'], "%H:%M").time()
    stop_loss = params['stop_loss']
    target_profit = params['target_profit']
    ratio_check = params['ratio_check']
    resize_factor = params['resize_factor']

    data = load_parquet_data(all_folders)

    train_rows = []
    total_pnl = 0

    for mainkey, subfolders in data.items():
        date_str, pnl, exit_reason, vix_close_value = run_for_one_day(
            mainkey, subfolders, start_time, exit_time, stop_loss, target_profit, ratio_check, resize_factor
        )

        if pnl is not None:
            train_rows.append({'date': date_str, 'pnl': pnl})
            total_pnl += pnl

    df_train = pd.DataFrame(train_rows)
    df_train = df_train.sort_values('date')
    df_train['pnl'] = pd.to_numeric(df_train['pnl'], errors='coerce').fillna(0)
    df_train['equity'] = df_train['pnl'].cumsum()

    metrics = process_performance_metrics(df_train)
    max_drawdown = metrics.get('max_drawdown', 0)

    Capital = int(os.getenv('Capital'))
    normalized_pnl = total_pnl / Capital

    #Adjust the multiplier i.e 15 and Max Drawdown Threshold i.e 0.09 as per your need.
    if max_drawdown > 0.09: 
        #risk_penalty = np.exp(15 * (max_drawdown - 0.09)) + 1   

        #Add a safety cap on the exponent input to prevent overflow.
        penalty_input = min(15 * (max_drawdown - 0.09), 50)
        risk_penalty = np.exp(penalty_input) + 1
    else:
        risk_penalty = 1  # No penalty if within your target
    
    final_score = normalized_pnl/risk_penalty
    
    
    return {
        'loss': -final_score,
        'status': STATUS_OK,
        'params': params,
        'total_pnl': total_pnl,
        'max_drawdown': max_drawdown,
        'risk_penalty': risk_penalty

    }


# ------------------ Main Optimization Routine ------------------ #
def run_hyperopt():
    global all_folders

    start_time_main = time.time()
    
    root_path = r"C:\Users\<USER>\YatinBhatia\Thursday_PT_V2\Parquet_Files\Thursday_output_folder"
    
    # Retrieve start and end dates from environment variables
    start_date_str = os.getenv("START_DATE")
    end_date_str = os.getenv("END_DATE")

    try:
        from_date = datetime.strptime(start_date_str, "%Y%m%d")
        to_date = datetime.strptime(end_date_str, "%Y%m%d")
    except ValueError as e:
        return
    
    start_time_list_date_folders = time.time()
    all_folders = list_date_folders(root_path, from_date, to_date)
    #print("All folders: ", all_folders)
    
    '''
    space = {
        'start_time': hp.choice('start_time', [f"09:{m:02d}" for m in range(30, 60, 5)]),
        'exit_time': hp.choice('exit_time', [f"14:{m:02d}" for m in range(45, 60, 5)] + [f"15:{m:02d}" for m in range(0, 20, 5)]),
        'stop_loss': hp.choice('stop_loss', [-0.5 - 0.1 * i for i in range(11)]),
        #'stop_loss': hp.choice('stop_loss', [-0.3 - 0.05 * i for i in range(8)]),  # Tighter stop losses
        'target_profit': hp.choice('target_profit', [1.0 + 0.1 * i for i in range(11)]),
        'ratio_check': hp.choice('ratio_check', [0.10 + 0.01 * i for i in range(11)]),
        'resize_factor': hp.choice('resize_factor', [0.3 + 0.1 * i for i in range(5)])
    }
    '''

    space = {
    # Broader start time range (if applicable)
    'start_time': hp.choice('start_time', [f"{h:02d}:{m:02d}" for h in range(9, 11) for m in range(0, 60, 5) if not (h == 9 and m < 15)]),    
   
    # Broader exit time range (up to market close)
    'exit_time': hp.choice('exit_time', [f"{h:02d}:{m:02d}" for h in range(14, 16) for m in range(0, 60, 5) if not (h == 15 and m > 29)]),

    # Much wider stop loss (more risk, but penalized in the objective)
    'stop_loss': hp.quniform('stop_loss', -2.0, -0.3, 0.05),  # From -2.0 to -0.3

    # Wider profit target (scalable with risk)
    'target_profit': hp.quniform('target_profit', 0.5, 3.0, 0.1),

    #try using loguniform for ratio_check and resize_factor
    'ratio_check': hp.loguniform('ratio_check', np.log(0.01), np.log(0.3)),      # Range: 0.01 to 0.3

    'resize_factor': hp.loguniform('resize_factor', np.log(0.1), np.log(1.0))  # Range: 0.1 to 1.0
    }

    trials = Trials()
    best = fmin(
        fn=lambda params: objective(params, all_folders), 
        space=space, 
        algo=tpe.suggest, 
        max_evals=50, 
        trials=trials,
    )

    print(f"Best Parameters Index: {best}")

    # Save all unique results
    save_results_to_csv(trials, 'hyperopt_results.csv')

    # Top 5 unique trials
    top_trials = get_unique_sorted_trials(trials)[:5]

    print("\nTop 5 Best Parameters:")
    for i, trial in enumerate(top_trials):
        params = trial['params']
        print(f"\nRank {i+1}:")
        for param, value in params.items():
            print(f"{param}: {value}")
        print(f"Total PnL: {trial.get('total_pnl', 0)}")
        print(f"Max Drawdown: {trial.get('max_drawdown', 0)}")
        print(f"Final Score: {-trial.get('loss', 0)}")



# ------------------ Entry Point ------------------ #
if __name__ == "__main__":
    import time
    start = time.time()
    print("Starting hyperparameter optimization...")
    run_hyperopt()
    print(f"Total execution time: {time.time() - start:.2f}s")
